import { useTheme } from '@app/context/theme';
import type { CategoryModel, UserModel } from '@app/database/models/server';
import ArabicNumbers from '@app/utils/englishNumberToArabic';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, Image, Dimensions, Pressable, DeviceEventEmitter, Keyboard, FlatList } from 'react-native';
import ProfilePicture from '../profile_picture';
import { PlusCircleIcon } from 'react-native-heroicons/outline';
import { dismissBottomSheet, dismissModal, goToScreen, openAsBottomSheet } from '@app/screens/navigation';
import { Events, Screens } from '@app/constants';
import { useIntl } from 'react-intl';
import { useIsTablet } from '@app/hooks/device';
import { updateLocalCustomStatus } from '@actions/local/user';
import { useServerUrl } from '@app/context/server';
import { getUserCustomStatus } from '@app/utils/user';
import { CustomStatusDurationEnum, SET_CUSTOM_STATUS_FAILURE } from '@app/constants/custom_status';
import { generateId } from '@app/utils/general';
import { fetchStatus, updateCustomStatus } from '@actions/remote/user';
import CategoryBody from '@app/screens/home/<USER>/categories_list/categories/body';
import CustomStatus from '../channel_item/custom_status';
import { ScrollView } from 'react-native-gesture-handler';

type StatusProps = {

    currentUser?: UserModel | undefined,
    channels: CategoryModel[]
}

const StatusComponent = ({

    currentUser = undefined,
    channels
}: StatusProps) => {

    const listRef = useRef<FlatList>(null);
    const extractKey = (item: CategoryModel | 'UNREADS') => (item === 'UNREADS' ? 'UNREADS' : item.id);

    // Deduplicate channels to prevent duplicate keys
    const uniqueChannels = useMemo(() => {
        return channels.filter((channel, index, self) =>
            index === self.findIndex(c => c.id === channel.id)
        );
    }, [channels]);


    const theme = useTheme()
    const intl = useIntl();
    const isTablet = useIsTablet();
    const serverUrl = useServerUrl()
    const windowWidth = Dimensions.get('window').width;
    const bottomSheetId = 'PostOptions'
    const directMessageList = channels.filter(x => x.displayName !== 'Direct Messages')
    const Favorites = channels.filter(x => x.displayName !== 'Favorites')


    const gotoStatusCreation = () => {
        goToScreen(Screens.USER_STATUS_CREATION,
            'Status',
            { userID: currentUser?.id },
            {
                topBar: {
                    visible: false,
                    backButton: {
                        visible: true,
                        color: theme.sidebarHeaderTextColor,
                    },
                    background: {
                        color: '#00987e'
                    },
                    elevation: 0
                },
                layout: {
                    componentBackgroundColor: theme.centerChannelBg,
                },
                statusBar: {
                    visible: true,
                    backgroundColor: "#00987e",
                    style: 'light'
                },
            }
        );
    }

    const renderCategory = useCallback((data: { item: CategoryModel }) => {
        return (
            <>
                {

                    <CategoryBody

                        isShowOnlyState={true}
                        category={data.item}
                        isTablet={isTablet}
                        locale={intl.locale}
                        onChannelSwitch={() => { }}
                    />

                }
            </>
        );
    }, [intl.locale, isTablet,]);



    return (

        <View
            style={{
                marginTop: 10,
                paddingHorizontal: 3,
                start: 0,
                width: windowWidth,
                // backgroundColor:'green',
                height: 115, // Reduced height to fit better in the allocated space
                alignItems: 'flex-start',
                flexDirection: 'row',
                overflow: 'visible' // Ensure content is not clipped
            }}
        >
            <Pressable
                onPress={() => { gotoStatusCreation() }}
            >

                <View
                    style={{
                        padding: 3,
                        borderRadius: 30,
                        borderWidth: 2,
                        borderColor: theme.buttonBg,
                        start: 10,
                        top: 5,
                        height: 50,
                        width: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginEnd: 11
                    }}
                >

                    <ProfilePicture
                        author={currentUser}
                        showStatus={false}
                        size={38}
                    />


                </View>

                <PlusCircleIcon
                    style={{
                        backgroundColor: theme.buttonBg,

                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 15,
                        position: 'absolute',
                        bottom: -10,
                        start: 5,
                        borderColor: 'white',
                        borderWidth: 2
                    }}
                    color={theme.centerChannelBg}
                    size={24}
                />


            </Pressable>

            <View style={{ width: 0 }} />



            {channels?.length > 0 &&
                <View
                    style={{
                        paddingStart: 5,
                        width: windowWidth - 50,
                        // flex: 1,
                        //alignItems: 'flex-start',
                        // height: 55,
                        // backgroundColor: 'red'
                    }}
                >
                    {/* case 'Favorites':
                return 'المفضلة'
            case 'Channels':
                return 'القنوات'
            case 'Direct Messages':
                return 'القنوات المباشرة'
                default:
                return "" */}


                    {/* <ScrollView
                    horizontal={true}
                    contentContainerStyle={{
                          alignItems:'flex-start',
                          height:150,
                          paddingStart:'auto'
                    }}
                    
                   > */}
                    {/* <View style={{flexDirection:'row'}}> */}
                    <FlatList
                        horizontal={true}
                        data={uniqueChannels}
                        ref={listRef}
                        renderItem={renderCategory}
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={extractKey}
                        initialNumToRender={Math.min(uniqueChannels.length, 5)} // Optimize initial render
                        // @ts-expect-error strictMode not included in the types
                        strictMode={true}
                        contentContainerStyle={{
                            paddingHorizontal: 5,
                            minWidth: windowWidth - 80 // Ensure minimum width but allow expansion for scrolling
                        }}
                        style={{
                            flexGrow: 1 // Allow the FlatList to grow and enable scrolling
                        }}
                    />

                    {/* </View> */}
                    {/* {channels.map((item) => (
                    
                    <CategoryBody
                      isShowOnlyState={true}
                        key={item.id}
                        category={item}
                        isTablet={isTablet}
                        locale={intl.locale}
                        onChannelSwitch={() => {}}
                    />
                 ))}
                    
                   </ScrollView> */}
                </View>}


        </View>

    );
};

export default StatusComponent;
