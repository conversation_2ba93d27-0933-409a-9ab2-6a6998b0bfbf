import { useTheme } from '@app/context/theme';
import type { CategoryModel, UserModel } from '@app/database/models/server';
import ArabicNumbers from '@app/utils/englishNumberToArabic';
import React, { useCallback, useMemo, useRef } from 'react';
import { View, Text, Image, Dimensions, Pressable, DeviceEventEmitter, Keyboard, FlatList } from 'react-native';
import ProfilePicture from '../profile_picture';
import { PlusCircleIcon } from 'react-native-heroicons/outline';
import { dismissBottomSheet, dismissModal, openAsBottomSheet } from '@app/screens/navigation';
import { Events, Screens } from '@app/constants';
import { useIntl } from 'react-intl';
import { useIsTablet } from '@app/hooks/device';
import { updateLocalCustomStatus } from '@actions/local/user';
import { useServerUrl } from '@app/context/server';
import { getUserCustomStatus } from '@app/utils/user';
import { CustomStatusDurationEnum, SET_CUSTOM_STATUS_FAILURE } from '@app/constants/custom_status';
import { generateId } from '@app/utils/general';
import { updateCustomStatus } from '@actions/remote/user';
import CategoryBody from '@app/screens/home/<USER>/categories_list/categories/body';
import CustomStatus from '../channel_item/custom_status';

type StatusProps = {
    currentUser?: UserModel | undefined,
    channels: CategoryModel[]
}

const StatusComponent = ({
    currentUser = undefined,
    channels
}: StatusProps) => {

    const listRef = useRef<FlatList>(null);
    const extractKey = (item: CategoryModel | 'UNREADS') => (item === 'UNREADS' ? 'UNREADS' : item.id);

    // Deduplicate channels to prevent duplicate keys
    const uniqueChannels = useMemo(() => {
        return channels.filter((channel, index, self) =>
            index === self.findIndex(c => c.id === channel.id)
        );
    }, [channels]);


    const theme = useTheme()
    const intl = useIntl();
    const isTablet = useIsTablet();
    const serverUrl = useServerUrl()
    const windowWidth = Dimensions.get('window').width;
    const bottomSheetId = 'PostOptions'



    const handleEmojiPress = useCallback(async (emoji: string) => {
        await dismissBottomSheet(bottomSheetId);
        await handleSetStatus(emoji)
    }, [bottomSheetId,]);

    const openEmojiPicker = useCallback(async () => {
        await dismissBottomSheet(bottomSheetId);
        openAsBottomSheet({
            closeButtonId: 'close-add-reaction',
            screen: Screens.EMOJI_PICKER,
            theme,
            title: intl.formatMessage({ id: 'mobile.post_info.add_reaction', defaultMessage: 'Add Reaction' }),
            props: { onEmojiPress: handleEmojiPress },
        });
    }, [handleEmojiPress, intl, theme]);


    const handleSetStatus = useCallback(async (emoji: string) => {

        //console.log(`\n\nthis the emoji name ${emoji}\n\n`)
        const status: UserCustomStatus = {
            emoji: emoji || 'speech_balloon',
            text: '',
            duration: CustomStatusDurationEnum.DONT_CLEAR,
        };


        const { error } = await updateCustomStatus(serverUrl, status);
        if (error) {
            DeviceEventEmitter.emit(SET_CUSTOM_STATUS_FAILURE);
            return;
        }

        updateLocalCustomStatus(serverUrl, currentUser!, status);


        Keyboard.dismiss();
        if (isTablet) {
            DeviceEventEmitter.emit(Events.ACCOUNT_SELECT_TABLET_VIEW, '');
        } else {
            dismissModal();
        }
    }, [currentUser]);

    const renderCategory = useCallback((data: { item: CategoryModel }) => {

        return (
            <>

                <CategoryBody

                    isShowOnlyState={true}
                    category={data.item}
                    isTablet={isTablet}
                    locale={intl.locale}
                    onChannelSwitch={() => { }}
                />
            </>
        );
    }, [intl.locale, isTablet,]);


    return (

        <View
            style={{
                marginTop: 10,
                paddingHorizontal: 10,
                start: 0,
                width: windowWidth,
                //backgroundColor:'green',
               // height: 50,
                alignItems: 'flex-start',
                flexDirection: 'row'
            }}
        >
            <Pressable
                onPress={openEmojiPicker}
            >

                <View
                    style={{
                        padding: 3,
                        borderRadius: 30,
                        borderWidth: 2,
                        borderColor: theme.buttonBg,
                        start: 10,
                        height: 50,
                        width: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginEnd: 11
                    }}
                >

                    <ProfilePicture
                        author={currentUser}
                        showStatus={false}
                        size={28}
                    />


                </View>

                <PlusCircleIcon
                    style={{
                        backgroundColor: theme.buttonBg,

                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 15,
                        position: 'absolute',
                        bottom: -5,
                        start: 5
                    }}
                    color={theme.centerChannelBg}
                    size={24}
                />

            </Pressable>

            <View style={{ width: 0 }} />

            {uniqueChannels?.length > 0 &&
                <View
                    style={{
                        flex: 1,
                        alignItems: 'flex-start',
                       // paddingBottom:5
                        //marginTop:1,
                        //justifyContent:'flex-start'
                    }}>

                    <FlatList
                        data={uniqueChannels}
                        ref={listRef}
                        renderItem={renderCategory}
                        scrollEnabled={false}
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={extractKey}
                        initialNumToRender={uniqueChannels.length}
                        // @ts-expect-error strictMode not included in the types
                        strictMode={true}
                    />
                </View>}


        </View>

    );
};

export default StatusComponent;
