// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {createContext, useContext, useEffect, useRef, type PropsWithChildren} from 'react';
import {AppState, type AppStateStatus} from 'react-native';

import WebsocketManager from '@managers/websocket_manager';
import {useServerUrl} from '@context/server';
import {logDebug, logError} from '@utils/log';

interface WebSocketContextValue {
    isConnected: boolean;
    connectionState: WebsocketConnectedState;
    registerCleanup: (cleanup: () => void) => () => void;
}

const WebSocketContext = createContext<WebSocketContextValue | null>(null);

interface WebSocketProviderProps {
    /**
     * Whether to automatically manage connection lifecycle
     */
    autoManage?: boolean;
    
    /**
     * Custom error handler for WebSocket errors
     */
    onError?: (error: any) => void;
}

/**
 * Provider component that manages WebSocket connection lifecycle
 * and provides cleanup utilities to child components
 */
export const WebSocketProvider: React.FC<PropsWithChildren<WebSocketProviderProps>> = ({
    children,
    autoManage = true,
    onError,
}) => {
    const serverUrl = useServerUrl();
    const cleanupFunctionsRef = useRef<Set<() => void>>(new Set());
    const connectionStateRef = useRef<WebsocketConnectedState>('not_connected');
    const subscriptionRef = useRef<any>(null);
    
    // Register cleanup function
    const registerCleanup = (cleanup: () => void): (() => void) => {
        cleanupFunctionsRef.current.add(cleanup);
        
        // Return unregister function
        return () => {
            cleanupFunctionsRef.current.delete(cleanup);
        };
    };
    
    // Execute all cleanup functions
    const executeCleanup = () => {
        cleanupFunctionsRef.current.forEach((cleanup) => {
            try {
                cleanup();
            } catch (error) {
                logError('Error executing WebSocket cleanup function', error);
                onError?.(error);
            }
        });
    };
    
    // Setup WebSocket state subscription
    useEffect(() => {
        if (!autoManage) {
            return;
        }
        
        subscriptionRef.current = WebsocketManager.observeWebsocketState(serverUrl).subscribe({
            next: (state) => {
                const previousState = connectionStateRef.current;
                connectionStateRef.current = state;
                
                logDebug(`WebSocket state changed: ${previousState} -> ${state}`);
                
                // Execute cleanup when connection is lost
                if (previousState === 'connected' && state === 'not_connected') {
                    executeCleanup();
                }
            },
            error: (error) => {
                logError('WebSocket state subscription error', error);
                onError?.(error);
            }
        });
        
        return () => {
            if (subscriptionRef.current) {
                subscriptionRef.current.unsubscribe();
                subscriptionRef.current = null;
            }
        };
    }, [serverUrl, autoManage, onError]);
    
    // Cleanup on unmount
    useEffect(() => {
        return () => {
            logDebug('WebSocketProvider unmounting, executing cleanup');
            executeCleanup();
            cleanupFunctionsRef.current.clear();
        };
    }, []);
    
    const contextValue: WebSocketContextValue = {
        isConnected: WebsocketManager.isConnected(serverUrl),
        connectionState: connectionStateRef.current,
        registerCleanup,
    };
    
    return (
        <WebSocketContext.Provider value={contextValue}>
            {children}
        </WebSocketContext.Provider>
    );
};

/**
 * Hook to access WebSocket context
 */
export const useWebSocketContext = (): WebSocketContextValue => {
    const context = useContext(WebSocketContext);
    if (!context) {
        throw new Error('useWebSocketContext must be used within a WebSocketProvider');
    }
    return context;
};

/**
 * Higher-order component that wraps a component with WebSocket lifecycle management
 */
export const withWebSocketCleanup = <P extends object>(
    Component: React.ComponentType<P>,
    options: WebSocketProviderProps = {}
) => {
    const WrappedComponent: React.FC<P> = (props) => {
        return (
            <WebSocketProvider {...options}>
                <Component {...props} />
            </WebSocketProvider>
        );
    };
    
    WrappedComponent.displayName = `withWebSocketCleanup(${Component.displayName || Component.name})`;
    
    return WrappedComponent;
};

/**
 * Hook for components that need to register cleanup functions
 */
export const useWebSocketCleanupRegistry = () => {
    const {registerCleanup} = useWebSocketContext();
    
    const registerCleanupCallback = (cleanup: () => void, deps: React.DependencyList = []) => {
        useEffect(() => {
            const unregister = registerCleanup(cleanup);
            return unregister;
        }, deps);
    };
    
    return {registerCleanupCallback};
};

/**
 * Component that automatically cleans up when WebSocket disconnects
 */
export const WebSocketCleanupBoundary: React.FC<PropsWithChildren<{
    onCleanup: () => void;
    onError?: (error: any) => void;
}>> = ({children, onCleanup, onError}) => {
    const {registerCleanup} = useWebSocketContext();
    
    useEffect(() => {
        const unregister = registerCleanup(() => {
            try {
                onCleanup();
            } catch (error) {
                logError('Error in WebSocketCleanupBoundary', error);
                onError?.(error);
            }
        });
        
        return unregister;
    }, [onCleanup, onError, registerCleanup]);
    
    return <>{children}</>;
};
