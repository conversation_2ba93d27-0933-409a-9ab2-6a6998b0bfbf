import { useTheme } from "@app/context/theme";
import { StatusBar } from "react-native";
import tinycolor from "tinycolor2";

const  StateBarComponent=()=>{
    const theme = useTheme()
  const isDark = !(theme.centerChannelColor==='#ffffff');

  return (
         <StatusBar
          // backgroundColor={(theme.centerChannelColor==='#0d0d0d')?'#ffffff':'#222227'}
          // translucent={false}
          // barStyle={!(theme.centerChannelColor==='#0d0d0d')?'dark-content':'light-content'}
          />

  )
}

export default StateBarComponent
