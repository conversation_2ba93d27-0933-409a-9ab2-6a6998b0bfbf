// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import { FlatList } from '@stream-io/flat-list-mvcp';
import React, { type ReactElement, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DeviceEventEmitter, type ListRenderItemInfo, Platform, type StyleProp, StyleSheet, type ViewStyle, type NativeSyntheticEvent, type NativeScrollEvent } from 'react-native';
import Animated, { type AnimatedStyle } from 'react-native-reanimated';

import { fetchPosts, fetchPostThread } from '@actions/remote/post';
import CombinedUserActivity from '@components/post_list/combined_user_activity';
import DateSeparator from '@components/post_list/date_separator';
import NewMessagesLine from '@components/post_list/new_message_line';
import Post from '@components/post_list/post';
import ThreadOverview from '@components/post_list/thread_overview';
import { Events, Screens } from '@constants';
import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { getDateForDateLine, preparePostList } from '@utils/post_list';

import { INITIAL_BATCH_TO_RENDER_POST_LIST, SCROLL_POSITION_CONFIG, VIEWABILITY_CONFIG_POST_LIST } from './config';
import { PERFORMANCE_PRESETS, END_REACHED_THRESHOLD } from '@constants/flatlist_performance';
import MoreMessages from './more_messages';
import ScrollToEndView from './scroll_to_end_view';

import type { PostListItem, PostListOtherItem, ViewableItemsChanged, ViewableItemsChangedListenerEvent } from '@typings/components/post_list';
import type PostModel from '@typings/database/models/servers/post';

type Props = {
    isRecoding?: boolean | null;
    appsEnabled: boolean;
    channelId: string;
    contentContainerStyle?: StyleProp<AnimatedStyle<ViewStyle>>;
    currentTimezone: string | null;
    currentUserId: string;
    currentUsername: string;
    customEmojiNames: string[];
    disablePullToRefresh?: boolean;
    highlightedId?: PostModel['id'];
    highlightPinnedOrSaved?: boolean;
    isCRTEnabled?: boolean;
    isPostAcknowledgementEnabled?: boolean;
    lastViewedAt: number;
    location: string;
    nativeID: string;
    onEndReached?: () => void;
    posts: PostModel[];
    rootId?: string;
    shouldRenderReplyButton?: boolean;
    shouldShowJoinLeaveMessages: boolean;
    showMoreMessages?: boolean;
    showNewMessageLine?: boolean;
    footer?: ReactElement;
    header?: ReactElement;
    testID: string;
    currentCallBarVisible?: boolean;
    savedPostIds: Set<string>;
    setLastViewedFileInfo?: (fileInfo: FileInfo) => void | undefined;
    channelType?: ChannelType | undefined;
}

type onScrollEndIndexListenerEvent = (endIndex: number) => void;

type ScrollIndexFailed = {
    index: number;
    highestMeasuredFrameIndex: number;
    averageItemLength: number;
};

const CONTENT_OFFSET_THRESHOLD = 160;

const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);
const keyExtractor = (item: PostListItem | PostListOtherItem) => (item.type === 'post' ? item.value.currentPost.id : item.value);

const styles = StyleSheet.create({
    flex: {
        flex: 1,
    },
    container: {
        flex: 1,
    },
});

const PostList = ({
    isRecoding,
    appsEnabled,
    channelId,
    contentContainerStyle,
    currentTimezone,
    currentUserId,
    currentUsername,
    customEmojiNames,
    disablePullToRefresh,
    footer,
    header,
    highlightedId,
    highlightPinnedOrSaved = true,
    isCRTEnabled,
    isPostAcknowledgementEnabled,
    lastViewedAt,
    location,
    nativeID,
    onEndReached,
    posts,
    rootId,
    shouldRenderReplyButton = true,
    shouldShowJoinLeaveMessages,
    showMoreMessages,
    showNewMessageLine = true,
    testID,
    savedPostIds,
    setLastViewedFileInfo = undefined,
    channelType = undefined
}: Props) => {
    const listRef = useRef<FlatList<string | PostModel>>(null);
    const onScrollEndIndexListener = useRef<onScrollEndIndexListenerEvent>();
    const onViewableItemsChangedListener = useRef<ViewableItemsChangedListenerEvent>();
    const scrolledToHighlighted = useRef(false);
    const [refreshing, setRefreshing] = useState(false);
    const [showScrollToEndBtn, setShowScrollToEndBtn] = useState(false);
    const [lastPostId, setLastPostId] = useState<string | undefined>(posts[0]?.id);
    const theme = useTheme();
    const serverUrl = useServerUrl();

    const orderedPosts = useMemo(() => {
        return preparePostList(posts, lastViewedAt, showNewMessageLine, currentUserId, currentUsername, shouldShowJoinLeaveMessages, currentTimezone, location === Screens.THREAD, savedPostIds);
    }, [posts, lastViewedAt, showNewMessageLine, currentTimezone, currentUsername, shouldShowJoinLeaveMessages, location, savedPostIds]);

    const initialIndex = useMemo(() => {
        return orderedPosts.findIndex((i) => i.type === 'start-of-new-messages');
    }, [orderedPosts]);

    const isNewMessage = useMemo(() => {
        if (!lastPostId) {
            return false;
        }
        return posts[0]?.id !== lastPostId;
    }, [posts[0]?.id, lastPostId]);

    useEffect(() => {
        const t = setTimeout(() => {
            listRef.current?.scrollToOffset({ offset: 0, animated: true });
        }, 300);

        return () => clearTimeout(t);
    }, [channelId, rootId]);

    useEffect(() => {
        const scrollToBottom = (screen: string) => {
            if (screen === location) {
                const scrollToBottomTimer = setTimeout(() => {
                    listRef.current?.scrollToOffset({ offset: 0, animated: true });
                }, 400);
            }
        };

        const scrollBottomListener = DeviceEventEmitter.addListener(Events.POST_LIST_SCROLL_TO_BOTTOM, scrollToBottom);

        return () => {
            scrollBottomListener.remove();
        };
    }, []);

    const onRefresh = useCallback(async () => {
        if (disablePullToRefresh) {
            return;
        }
        setRefreshing(true);
        if (location === Screens.CHANNEL && channelId) {
            await fetchPosts(serverUrl, channelId);
        } else if (location === Screens.THREAD && rootId) {
            const options: FetchPaginatedThreadOptions = {};
            const lastPost = posts[0];
            if (lastPost) {
                options.fromCreateAt = lastPost.createAt;
                options.fromPost = lastPost.id;
                options.direction = 'down';
            }
            await fetchPostThread(serverUrl, rootId, options);
        }
        setRefreshing(false);
    }, [channelId, location, posts, rootId, disablePullToRefresh]);

    const onScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const { y } = event.nativeEvent.contentOffset;
        if (y === 0) {
            setLastPostId(posts[0]?.id);
        }
        setShowScrollToEndBtn(y > CONTENT_OFFSET_THRESHOLD);
    }, [posts[0]?.id]);

    const onScrollToIndexFailed = useCallback((info: ScrollIndexFailed) => {
        const index = Math.min(info.highestMeasuredFrameIndex, info.index);

        if (!highlightedId) {
            if (onScrollEndIndexListener.current) {
                onScrollEndIndexListener.current(index);
            }
            scrollToIndex(index);
        }
    }, [highlightedId]);

    const onViewableItemsChanged = useCallback(({ viewableItems }: ViewableItemsChanged) => {
        if (!viewableItems.length) {
            return;
        }

        const viewableItemsMap = viewableItems.reduce((acc: Record<string, boolean>, { item, isViewable }) => {
            if (isViewable && item.type === 'post') {
                acc[`${location}-${item.value.currentPost.id}`] = true;
            }
            return acc;
        }, {});

        DeviceEventEmitter.emit(Events.ITEM_IN_VIEWPORT, viewableItemsMap);

        if (onViewableItemsChangedListener.current) {
            onViewableItemsChangedListener.current(viewableItems);
        }
    }, [location]);

    const registerScrollEndIndexListener = useCallback((listener: onScrollEndIndexListenerEvent) => {
        onScrollEndIndexListener.current = listener;
        const removeListener = () => {
            onScrollEndIndexListener.current = undefined;
        };

        return removeListener;
    }, []);

    const registerViewableItemsListener = useCallback((listener: ViewableItemsChangedListenerEvent) => {
        onViewableItemsChangedListener.current = listener;
        const removeListener = () => {
            onViewableItemsChangedListener.current = undefined;
        };

        return removeListener;
    }, []);

    const renderItem = useCallback(({ item }: ListRenderItemInfo<PostListItem | PostListOtherItem>) => {
        switch (item.type) {
            case 'start-of-new-messages':
                return (
                    <NewMessagesLine
                        key={item.value}
                        theme={theme}
                        testID={`${testID}.new_messages_line`}
                    />
                );
            case 'date':
                return (
                    <DateSeparator
                        key={item.value}
                        date={getDateForDateLine(item.value)}
                        timezone={currentTimezone}
                    />
                );
            case 'thread-overview':
                return (
                    <ThreadOverview
                        key={item.value}
                        rootId={rootId!}
                        testID={`${testID}.thread_overview`}
                    />
                );
            case 'user-activity': {
                const postProps = {
                    currentUsername,
                    key: item.value,
                    postId: item.value,
                    location,
                    style: styles.container,
                    testID: `${testID}.combined_user_activity`,
                    showJoinLeave: shouldShowJoinLeaveMessages,
                    theme,
                };

                return (
                <CombinedUserActivity {...postProps} />);
            }
            default: {
                const post = item.value.currentPost;
                const { isSaved, nextPost, previousPost } = item.value;
                const skipSaveddHeader = (location === Screens.THREAD && post.id === rootId);
                const postProps = {
                    appsEnabled,
                    customEmojiNames,
                    isCRTEnabled,
                    isPostAcknowledgementEnabled,
                    highlight: highlightedId === post.id,
                    highlightPinnedOrSaved,
                    isSaved,
                    key: post.id,
                    location,
                    nextPost,
                    post,
                    previousPost,
                    rootId,
                    shouldRenderReplyButton,
                    skipSaveddHeader,
                    testID: `${testID}.post`,
                    isRecoding,
                    setLastViewedFileInfo,
                    channelType
                };

                return (<Post {...postProps} />);
            }
        }
    }, [appsEnabled, currentTimezone, customEmojiNames, highlightPinnedOrSaved, isCRTEnabled, isPostAcknowledgementEnabled, shouldRenderReplyButton, theme]);

    const scrollToIndex = useCallback((index: number, animated = true, applyOffset = true) => {
        listRef.current?.scrollToIndex({
            animated,
            index,
            viewOffset: applyOffset ? Platform.select({ ios: -45, default: 0 }) : 0,
            viewPosition: 1,
        });
    }, []);

    const onScrollToEnd = useCallback(() => {
        listRef.current?.scrollToOffset({ offset: 0, animated: true });
    }, []);

    useEffect(() => {
        const t = setTimeout(() => {
            if (highlightedId && orderedPosts && !scrolledToHighlighted.current) {
                scrolledToHighlighted.current = true;
                const index = orderedPosts.findIndex((p) => p.type === 'post' && p.value.currentPost.id === highlightedId);
                if (index >= 0 && listRef.current) {
                    listRef.current?.scrollToIndex({
                        animated: true,
                        index,
                        viewOffset: 0,
                        viewPosition: 0.5,
                    });
                }
            }
        }, 500);

        return () => clearTimeout(t);
    }, [orderedPosts, highlightedId]);

    return (
        <>
            <AnimatedFlatList
                contentContainerStyle={contentContainerStyle}
                data={orderedPosts}
                keyboardDismissMode='interactive'
                keyboardShouldPersistTaps='handled'
                keyExtractor={keyExtractor}
                initialNumToRender={INITIAL_BATCH_TO_RENDER_POST_LIST}
                ListHeaderComponent={header}
                ListFooterComponent={footer}
                maintainVisibleContentPosition={SCROLL_POSITION_CONFIG}
                maxToRenderPerBatch={PERFORMANCE_PRESETS.COMPLEX_LIST.maxToRenderPerBatch}
                nativeID={nativeID}
                onEndReached={onEndReached}
                onEndReachedThreshold={END_REACHED_THRESHOLD.LATE}
                onScroll={onScroll}
                onScrollToIndexFailed={onScrollToIndexFailed}
                onViewableItemsChanged={onViewableItemsChanged}
                ref={listRef}
                removeClippedSubviews={PERFORMANCE_PRESETS.COMPLEX_LIST.removeClippedSubviews}
                renderItem={renderItem}
                scrollEventThrottle={PERFORMANCE_PRESETS.COMPLEX_LIST.scrollEventThrottle}
                style={styles.flex}
                updateCellsBatchingPeriod={PERFORMANCE_PRESETS.COMPLEX_LIST.updateCellsBatchingPeriod}
                viewabilityConfig={VIEWABILITY_CONFIG_POST_LIST}
                windowSize={PERFORMANCE_PRESETS.COMPLEX_LIST.windowSize}
                testID={`${testID}.flat_list`}
                inverted={true}
                refreshing={refreshing}
                onRefresh={onRefresh}
            />
            {location !== Screens.PERMALINK &&
                <ScrollToEndView
                    onPress={onScrollToEnd}
                    isNewMessage={isNewMessage}
                    showScrollToEndBtn={showScrollToEndBtn}
                    location={location}
                />
            }
            {showMoreMessages &&
                <MoreMessages
                    channelId={channelId}
                    isCRTEnabled={isCRTEnabled}
                    newMessageLineIndex={initialIndex}
                    posts={orderedPosts}
                    registerScrollEndIndexListener={registerScrollEndIndexListener}
                    registerViewableItemsListener={registerViewableItemsListener}
                    rootId={rootId}
                    scrollToIndex={scrollToIndex}
                    theme={theme}
                    testID={`${testID}.more_messages_button`}
                />
            }
        </>
    );
};

export default PostList;
