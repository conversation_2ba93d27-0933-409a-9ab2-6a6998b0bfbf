// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import React, {useCallback, useRef} from 'react';
import {FlatList} from 'react-native-gesture-handler';

import {PERFORMANCE_PRESETS, KEY_EXTRACTORS, createGetItemLayout} from '@constants/flatlist_performance';
import {useIsTablet} from '@hooks/device';

import UserListItem, {USER_ROW_HEIGHT} from './user_list_item';

import type UserModel from '@typings/database/models/servers/user';
import {View, type ListRenderItemInfo} from 'react-native';

type Props = {
    channelId: string;
    location: string;
    users: UserModel[];
    userAcknowledgements: Record<string, number>;
    timezone?: UserTimezone;
};

const UsersList = ({channelId, location, users, userAcknowledgements, timezone}: Props) => {
    const isTablet = useIsTablet();
    const listRef = useRef<FlatList>(null);

    const getItemLayout = createGetItemLayout(USER_ROW_HEIGHT);

    const renderItem = useCallback(({item}: ListRenderItemInfo<UserModel>) => (
        <UserListItem
            channelId={channelId}
            location={location}
            user={item}
            userAcknowledgement={userAcknowledgements[item.id]}
            timezone={timezone}
        />
    ), [channelId, location, timezone]);
  if(users?.length<=0)
        return(<View></View>)

    if (isTablet) {
        return (
            <FlatList
                data={users}
                getItemLayout={getItemLayout}
                initialNumToRender={PERFORMANCE_PRESETS.SIMPLE_LIST.initialNumToRender}
                keyExtractor={KEY_EXTRACTORS.ID}
                maxToRenderPerBatch={PERFORMANCE_PRESETS.SIMPLE_LIST.maxToRenderPerBatch}
                overScrollMode={'always'}
                ref={listRef}
                removeClippedSubviews={PERFORMANCE_PRESETS.SIMPLE_LIST.removeClippedSubviews}
                renderItem={renderItem}
                scrollEventThrottle={PERFORMANCE_PRESETS.SIMPLE_LIST.scrollEventThrottle}
                updateCellsBatchingPeriod={PERFORMANCE_PRESETS.SIMPLE_LIST.updateCellsBatchingPeriod}
                windowSize={PERFORMANCE_PRESETS.SIMPLE_LIST.windowSize}
            />
        );
    }

    return (
        <BottomSheetFlatList
            data={users}
            getItemLayout={getItemLayout}
            initialNumToRender={PERFORMANCE_PRESETS.SIMPLE_LIST.initialNumToRender}
            keyExtractor={KEY_EXTRACTORS.ID}
            maxToRenderPerBatch={PERFORMANCE_PRESETS.SIMPLE_LIST.maxToRenderPerBatch}
            overScrollMode={'always'}
            removeClippedSubviews={PERFORMANCE_PRESETS.SIMPLE_LIST.removeClippedSubviews}
            renderItem={renderItem}
            updateCellsBatchingPeriod={PERFORMANCE_PRESETS.SIMPLE_LIST.updateCellsBatchingPeriod}
            windowSize={PERFORMANCE_PRESETS.SIMPLE_LIST.windowSize}
        />
    );
};

export default UsersList;
