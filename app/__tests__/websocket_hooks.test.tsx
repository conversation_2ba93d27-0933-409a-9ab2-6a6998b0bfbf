// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {render, cleanup, act} from '@testing-library/react-native';
import {jest} from '@jest/globals';

import {useWebSocket, useWebSocketCleanup, useWebSocketState} from '@hooks/websocket';
import {WebSocketProvider, useWebSocketContext} from '@components/websocket_provider';

// Mock dependencies
jest.mock('@managers/websocket_manager');
jest.mock('@context/server', () => ({
    useServerUrl: () => 'https://test.mattermost.com',
}));
jest.mock('@react-navigation/native', () => ({
    useFocusEffect: (callback: () => void) => {
        React.useEffect(callback, []);
    },
    useNavigation: () => ({
        addListener: jest.fn(() => jest.fn()),
    }),
}));

// Mock WebSocket Manager
const mockWebsocketManager = {
    isConnected: jest.fn(() => false),
    observeWebsocketState: jest.fn(() => ({
        subscribe: jest.fn(() => ({
            unsubscribe: jest.fn(),
        })),
    })),
};

jest.mock('@managers/websocket_manager', () => ({
    __esModule: true,
    default: mockWebsocketManager,
}));

describe('WebSocket Hooks Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanup();
    });

    describe('useWebSocket Hook', () => {
        const TestComponent: React.FC<{onCleanup?: () => void}> = ({onCleanup}) => {
            const {isConnected, setCleanupFunction} = useWebSocket({
                onCleanup,
            });

            React.useEffect(() => {
                if (onCleanup) {
                    setCleanupFunction(onCleanup);
                }
            }, [onCleanup, setCleanupFunction]);

            return <div data-testid="test-component">{isConnected ? 'Connected' : 'Disconnected'}</div>;
        };

        test('should initialize with correct connection state', () => {
            mockWebsocketManager.isConnected.mockReturnValue(true);

            const {getByTestId} = render(<TestComponent />);
            
            expect(getByTestId('test-component')).toHaveTextContent('Connected');
            expect(mockWebsocketManager.isConnected).toHaveBeenCalled();
        });

        test('should call cleanup function on unmount', () => {
            const cleanupFn = jest.fn();
            
            const {unmount} = render(<TestComponent onCleanup={cleanupFn} />);
            
            unmount();
            
            expect(cleanupFn).toHaveBeenCalled();
        });

        test('should observe connection state changes', () => {
            const mockSubscribe = jest.fn(() => ({
                unsubscribe: jest.fn(),
            }));
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            render(<TestComponent />);
            
            expect(mockWebsocketManager.observeWebsocketState).toHaveBeenCalled();
        });
    });

    describe('useWebSocketCleanup Hook', () => {
        const TestComponent: React.FC<{cleanup: () => void}> = ({cleanup}) => {
            useWebSocketCleanup(cleanup);
            return <div data-testid="cleanup-component">Test</div>;
        };

        test('should subscribe to WebSocket state changes', () => {
            const cleanupFn = jest.fn();
            const mockSubscribe = jest.fn(() => ({
                unsubscribe: jest.fn(),
            }));
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            render(<TestComponent cleanup={cleanupFn} />);
            
            expect(mockSubscribe).toHaveBeenCalled();
        });

        test('should call cleanup when connection is lost', () => {
            const cleanupFn = jest.fn();
            let stateCallback: (state: string) => void;
            
            const mockSubscribe = jest.fn((observer) => {
                if (typeof observer === 'function') {
                    stateCallback = observer;
                } else {
                    stateCallback = observer.next;
                }
                return {unsubscribe: jest.fn()};
            });
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            render(<TestComponent cleanup={cleanupFn} />);
            
            // Simulate connection loss
            act(() => {
                stateCallback('not_connected');
            });
            
            expect(cleanupFn).toHaveBeenCalled();
        });

        test('should unsubscribe on unmount', () => {
            const cleanupFn = jest.fn();
            const mockUnsubscribe = jest.fn();
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: jest.fn(() => ({
                    unsubscribe: mockUnsubscribe,
                })),
            });

            const {unmount} = render(<TestComponent cleanup={cleanupFn} />);
            
            unmount();
            
            expect(mockUnsubscribe).toHaveBeenCalled();
        });
    });

    describe('useWebSocketState Hook', () => {
        const TestComponent: React.FC = () => {
            const connectionState = useWebSocketState();
            return <div data-testid="state-component">{connectionState}</div>;
        };

        test('should track connection state changes', () => {
            let stateCallback: (state: string) => void;
            
            const mockSubscribe = jest.fn((callback) => {
                stateCallback = callback;
                return {unsubscribe: jest.fn()};
            });
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            const {getByTestId, rerender} = render(<TestComponent />);
            
            // Initial state
            expect(getByTestId('state-component')).toHaveTextContent('not_connected');
            
            // Simulate state change
            act(() => {
                stateCallback('connecting');
                rerender(<TestComponent />);
            });
            
            expect(getByTestId('state-component')).toHaveTextContent('connecting');
        });
    });

    describe('WebSocketProvider', () => {
        const TestChild: React.FC = () => {
            const {isConnected, registerCleanup} = useWebSocketContext();
            
            React.useEffect(() => {
                const cleanup = () => console.log('Cleanup called');
                return registerCleanup(cleanup);
            }, [registerCleanup]);
            
            return <div data-testid="provider-child">{isConnected ? 'Connected' : 'Disconnected'}</div>;
        };

        test('should provide WebSocket context to children', () => {
            mockWebsocketManager.isConnected.mockReturnValue(true);
            
            const {getByTestId} = render(
                <WebSocketProvider>
                    <TestChild />
                </WebSocketProvider>
            );
            
            expect(getByTestId('provider-child')).toHaveTextContent('Connected');
        });

        test('should execute cleanup functions on connection loss', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            let stateCallback: (state: string) => void;
            
            const mockSubscribe = jest.fn((observer) => {
                stateCallback = observer.next;
                return {unsubscribe: jest.fn()};
            });
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            render(
                <WebSocketProvider>
                    <TestChild />
                </WebSocketProvider>
            );
            
            // Simulate connection loss
            act(() => {
                stateCallback('not_connected');
            });
            
            expect(consoleSpy).toHaveBeenCalledWith('Cleanup called');
            consoleSpy.mockRestore();
        });

        test('should handle errors in cleanup functions', () => {
            const errorSpy = jest.spyOn(console, 'error').mockImplementation();
            const onError = jest.fn();
            
            const TestChildWithError: React.FC = () => {
                const {registerCleanup} = useWebSocketContext();
                
                React.useEffect(() => {
                    const cleanup = () => {
                        throw new Error('Cleanup error');
                    };
                    return registerCleanup(cleanup);
                }, [registerCleanup]);
                
                return <div>Test</div>;
            };

            let stateCallback: (state: string) => void;
            
            const mockSubscribe = jest.fn((observer) => {
                stateCallback = observer.next;
                return {unsubscribe: jest.fn()};
            });
            
            mockWebsocketManager.observeWebsocketState.mockReturnValue({
                subscribe: mockSubscribe,
            });

            render(
                <WebSocketProvider onError={onError}>
                    <TestChildWithError />
                </WebSocketProvider>
            );
            
            // Simulate connection loss
            act(() => {
                stateCallback('not_connected');
            });
            
            expect(onError).toHaveBeenCalled();
            errorSpy.mockRestore();
        });
    });
});
