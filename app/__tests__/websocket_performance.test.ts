// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {jest} from '@jest/globals';

import WebSocketClient from '@client/websocket';
import WebsocketManager from '@managers/websocket_manager';

// Mock dependencies
jest.mock('@mattermost/react-native-network-client');
jest.mock('@database/manager');
jest.mock('@utils/log');
jest.mock('react-native');
jest.mock('@react-native-community/netinfo');

describe('WebSocket Performance and Memory Leak Tests', () => {
    const serverUrl = 'https://test.mattermost.com';
    const token = 'test-token';

    beforeEach(() => {
        jest.clearAllMocks();
        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
    });

    describe('Memory Leak Prevention', () => {
        test('should not leak memory with multiple client creations and destructions', () => {
            const initialMemory = process.memoryUsage();
            
            // Create and destroy many clients
            for (let i = 0; i < 100; i++) {
                const client = new WebSocketClient(`${serverUrl}-${i}`, token);
                
                // Set up some callbacks and state
                client.setEventCallback(() => {});
                client.setFirstConnectCallback(() => {});
                client.setErrorCallback(() => {});
                
                // Mock some internal state
                (client as any).connectionTimeout = setTimeout(() => {}, 1000);
                (client as any).healthCheckInterval = setInterval(() => {}, 1000);
                
                // Destroy client
                client.destroy();
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage();
            
            // Memory usage should not have grown significantly
            // Allow for some variance due to test overhead
            const memoryGrowth = finalMemory.heapUsed - initialMemory.heapUsed;
            const maxAllowedGrowth = 10 * 1024 * 1024; // 10MB
            
            expect(memoryGrowth).toBeLessThan(maxAllowedGrowth);
        });

        test('should properly cleanup timers to prevent timer leaks', () => {
            const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
            const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
            const setIntervalSpy = jest.spyOn(global, 'setInterval');
            const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
            
            const client = new WebSocketClient(serverUrl, token);
            
            // Trigger timer creation
            (client as any).connectionTimeout = setTimeout(() => {}, 1000);
            (client as any).healthCheckInterval = setInterval(() => {}, 1000);
            
            const timeoutCallCount = setTimeoutSpy.mock.calls.length;
            const intervalCallCount = setIntervalSpy.mock.calls.length;
            
            client.destroy();
            
            // Should have cleared all timers
            expect(clearTimeoutSpy).toHaveBeenCalledTimes(timeoutCallCount);
            expect(clearIntervalSpy).toHaveBeenCalledTimes(intervalCallCount);
            
            setTimeoutSpy.mockRestore();
            clearTimeoutSpy.mockRestore();
            setIntervalSpy.mockRestore();
            clearIntervalSpy.mockRestore();
        });

        test('should handle rapid connection/disconnection cycles', async () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Mock connection interface
            const mockConn = {
                onOpen: jest.fn(),
                onClose: jest.fn(),
                onError: jest.fn(),
                onMessage: jest.fn(),
                open: jest.fn(),
                close: jest.fn(),
                invalidate: jest.fn(),
                readyState: 0,
            };
            
            (client as any).conn = mockConn;
            
            // Rapid connection cycles
            for (let i = 0; i < 50; i++) {
                client.close(false);
                await new Promise(resolve => setTimeout(resolve, 1));
                
                // Simulate connection attempt
                if (!client.getIsDestroyed()) {
                    (client as any).isConnecting = false;
                }
            }
            
            client.destroy();
            
            // Should handle all cycles without issues
            expect(client.getIsDestroyed()).toBe(true);
        });
    });

    describe('WebSocketManager Performance', () => {
        test('should handle multiple server connections efficiently', () => {
            const startTime = Date.now();
            const serverUrls: string[] = [];
            
            // Create multiple server connections
            for (let i = 0; i < 50; i++) {
                const url = `https://test${i}.mattermost.com`;
                serverUrls.push(url);
                WebsocketManager.createClient(url, token);
            }
            
            const creationTime = Date.now() - startTime;
            
            // Cleanup all clients
            const cleanupStartTime = Date.now();
            serverUrls.forEach(url => {
                WebsocketManager.invalidateClient(url);
            });
            const cleanupTime = Date.now() - cleanupStartTime;
            
            // Operations should be reasonably fast
            expect(creationTime).toBeLessThan(1000); // Less than 1 second
            expect(cleanupTime).toBeLessThan(500);   // Less than 0.5 seconds
        });

        test('should not accumulate observers over time', () => {
            const serverUrls: string[] = [];
            const subscriptions: any[] = [];
            
            // Create many observers
            for (let i = 0; i < 100; i++) {
                const url = `https://test${i}.mattermost.com`;
                serverUrls.push(url);
                
                const subscription = WebsocketManager.observeWebsocketState(url).subscribe();
                subscriptions.push(subscription);
            }
            
            // Unsubscribe all
            subscriptions.forEach(sub => sub.unsubscribe());
            
            // Cleanup clients
            serverUrls.forEach(url => {
                WebsocketManager.invalidateClient(url);
            });
            
            // Should not have accumulated internal state
            expect(Object.keys((WebsocketManager as any).clients)).toHaveLength(0);
        });

        test('should handle concurrent operations safely', async () => {
            const operations: Promise<any>[] = [];
            
            // Concurrent client creation
            for (let i = 0; i < 20; i++) {
                operations.push(
                    Promise.resolve().then(() => {
                        const url = `https://concurrent${i}.mattermost.com`;
                        const client = WebsocketManager.createClient(url, token);
                        return {url, client};
                    })
                );
            }
            
            const results = await Promise.all(operations);
            
            // All operations should complete successfully
            expect(results).toHaveLength(20);
            results.forEach(({url, client}) => {
                expect(client).toBeDefined();
                expect(WebsocketManager.getClient(url)).toBe(client);
            });
            
            // Cleanup
            results.forEach(({url}) => {
                WebsocketManager.invalidateClient(url);
            });
        });
    });

    describe('Error Handling Performance', () => {
        test('should handle error bursts without degradation', () => {
            const client = new WebSocketClient(serverUrl, token);
            const errorCallback = jest.fn();
            
            client.setErrorCallback(errorCallback);
            
            // Mock connection with error handler
            const mockConn = {
                onError: jest.fn(),
                onClose: jest.fn(),
                onOpen: jest.fn(),
                onMessage: jest.fn(),
                open: jest.fn(),
                close: jest.fn(),
                invalidate: jest.fn(),
                readyState: 1,
            };
            
            (client as any).conn = mockConn;
            
            const startTime = Date.now();
            
            // Simulate error burst
            const errorHandler = mockConn.onError.mock.calls[0]?.[0];
            if (errorHandler) {
                for (let i = 0; i < 100; i++) {
                    errorHandler({url: (client as any).url, error: `Error ${i}`});
                }
            }
            
            const processingTime = Date.now() - startTime;
            
            client.destroy();
            
            // Should handle errors quickly
            expect(processingTime).toBeLessThan(100); // Less than 100ms
            expect(errorCallback).toHaveBeenCalledTimes(100);
        });

        test('should recover from connection failures efficiently', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Mock failed connections
            let connectionAttempts = 0;
            const originalInitialize = client.initialize.bind(client);
            
            jest.spyOn(client, 'initialize').mockImplementation(async (...args) => {
                connectionAttempts++;
                if (connectionAttempts < 5) {
                    // Simulate connection failure
                    throw new Error('Connection failed');
                }
                return originalInitialize(...args);
            });
            
            const startTime = Date.now();
            
            // Attempt connection with retries
            client.initialize().catch(() => {
                // Expected to fail initially
            });
            
            // Should not take too long to give up or succeed
            setTimeout(() => {
                const recoveryTime = Date.now() - startTime;
                expect(recoveryTime).toBeLessThan(5000); // Less than 5 seconds
                client.destroy();
            }, 100);
        });
    });

    describe('Resource Cleanup Verification', () => {
        test('should not leave dangling references after cleanup', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Set up various references
            const callbacks = {
                event: jest.fn(),
                connect: jest.fn(),
                error: jest.fn(),
                close: jest.fn(),
            };
            
            client.setEventCallback(callbacks.event);
            client.setFirstConnectCallback(callbacks.connect);
            client.setErrorCallback(callbacks.error);
            client.setCloseCallback(callbacks.close);
            
            // Mock connection
            (client as any).conn = {
                close: jest.fn(),
                invalidate: jest.fn(),
            };
            
            client.destroy();
            
            // Verify all references are cleared
            expect((client as any).eventCallback).toBeUndefined();
            expect((client as any).firstConnectCallback).toBeUndefined();
            expect((client as any).errorCallback).toBeUndefined();
            expect((client as any).closeCallback).toBeUndefined();
            expect((client as any).conn).toBeUndefined();
        });

        test('should cleanup WebSocketManager completely', () => {
            // Create some clients and state
            const urls = ['https://test1.com', 'https://test2.com'];
            urls.forEach(url => {
                WebsocketManager.createClient(url, token);
            });
            
            // Create some observers
            const subscriptions = urls.map(url => 
                WebsocketManager.observeWebsocketState(url).subscribe()
            );
            
            // Destroy manager
            WebsocketManager.destroy();
            
            // Verify complete cleanup
            urls.forEach(url => {
                expect(WebsocketManager.getClient(url)).toBeUndefined();
                expect(WebsocketManager.isConnected(url)).toBe(false);
            });
            
            // Subscriptions should be completed
            subscriptions.forEach(sub => {
                expect(sub.closed).toBe(true);
            });
        });
    });
});
