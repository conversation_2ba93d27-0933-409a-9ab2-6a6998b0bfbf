// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {jest} from '@jest/globals';

import WebSocketClient from '@client/websocket';
import WebsocketManager from '@managers/websocket_manager';

// Mock dependencies
jest.mock('@mattermost/react-native-network-client');
jest.mock('@database/manager');
jest.mock('@utils/log');
jest.mock('react-native', () => ({
    AppState: {
        currentState: 'active',
        addEventListener: jest.fn(),
    },
    Platform: {
        OS: 'ios',
    },
}));

jest.mock('@react-native-community/netinfo', () => ({
    fetch: jest.fn(() => Promise.resolve({isConnected: true})),
    addEventListener: jest.fn(),
}));

describe('WebSocket Cleanup Tests', () => {
    let mockClient: jest.Mocked<WebSocketClient>;
    const serverUrl = 'https://test.mattermost.com';
    const token = 'test-token';

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Reset WebSocket manager state
        WebsocketManager.closeAll();
        
        // Create mock client
        mockClient = {
            initialize: jest.fn(),
            close: jest.fn(),
            invalidate: jest.fn(),
            destroy: jest.fn(),
            isConnected: jest.fn(() => false),
            getIsDestroyed: jest.fn(() => false),
            setFirstConnectCallback: jest.fn(),
            setEventCallback: jest.fn(),
            setReconnectCallback: jest.fn(),
            setReliableReconnectCallback: jest.fn(),
            setCloseCallback: jest.fn(),
            setErrorCallback: jest.fn(),
            setConnectingCallback: jest.fn(),
            setMissedEventsCallback: jest.fn(),
            sendUserTypingEvent: jest.fn(),
            getConnectionId: jest.fn(() => 'test-connection-id'),
        } as any;
    });

    describe('WebSocketClient Cleanup', () => {
        test('should properly cleanup on destroy', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Mock connection
            (client as any).conn = {
                close: jest.fn(),
                invalidate: jest.fn(),
                readyState: 1, // OPEN
            };
            
            // Mock timeout
            (client as any).connectionTimeout = setTimeout(() => {}, 1000);
            (client as any).healthCheckInterval = setInterval(() => {}, 1000);
            
            // Set some callbacks
            client.setEventCallback(() => {});
            client.setFirstConnectCallback(() => {});
            
            // Destroy client
            client.destroy();
            
            // Verify cleanup
            expect(client.getIsDestroyed()).toBe(true);
            expect((client as any).conn).toBeUndefined();
            expect((client as any).connectionTimeout).toBeUndefined();
            expect((client as any).healthCheckInterval).toBeUndefined();
            expect((client as any).eventCallback).toBeUndefined();
            expect((client as any).firstConnectCallback).toBeUndefined();
        });

        test('should prevent operations after destroy', () => {
            const client = new WebSocketClient(serverUrl, token);
            client.destroy();
            
            // These should not throw or cause issues
            client.initialize();
            client.sendUserTypingEvent('channel-id');
            
            expect(client.isConnected()).toBe(false);
        });

        test('should handle multiple destroy calls safely', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Multiple destroy calls should not cause issues
            client.destroy();
            client.destroy();
            client.destroy();
            
            expect(client.getIsDestroyed()).toBe(true);
        });
    });

    describe('WebSocketManager Cleanup', () => {
        test('should properly cleanup client on invalidation', () => {
            // Create and register client
            const client = WebsocketManager.createClient(serverUrl, token);
            
            // Mock client methods
            jest.spyOn(client, 'close');
            jest.spyOn(client, 'destroy');
            
            // Invalidate client
            WebsocketManager.invalidateClient(serverUrl);
            
            // Verify cleanup
            expect(client.close).toHaveBeenCalledWith(true);
            expect(client.destroy).toHaveBeenCalled();
            expect(WebsocketManager.getClient(serverUrl)).toBeUndefined();
        });

        test('should cleanup all resources on destroy', () => {
            // Create multiple clients
            const client1 = WebsocketManager.createClient(serverUrl, token);
            const client2 = WebsocketManager.createClient('https://test2.mattermost.com', token);
            
            // Mock client methods
            jest.spyOn(client1, 'destroy');
            jest.spyOn(client2, 'destroy');
            
            // Destroy manager
            WebsocketManager.destroy();
            
            // Verify all clients destroyed
            expect(client1.destroy).toHaveBeenCalled();
            expect(client2.destroy).toHaveBeenCalled();
            
            // Verify no clients remain
            expect(WebsocketManager.getClient(serverUrl)).toBeUndefined();
            expect(WebsocketManager.getClient('https://test2.mattermost.com')).toBeUndefined();
        });

        test('should handle destroyed clients in operations', () => {
            const client = WebsocketManager.createClient(serverUrl, token);
            
            // Mock client as destroyed
            jest.spyOn(client, 'getIsDestroyed').mockReturnValue(true);
            
            // These operations should handle destroyed clients gracefully
            expect(WebsocketManager.isConnected(serverUrl)).toBe(false);
            expect(WebsocketManager.getClient(serverUrl)).toBeUndefined();
            
            // Should not attempt to initialize destroyed client
            WebsocketManager.initializeClient(serverUrl);
            expect(client.initialize).not.toHaveBeenCalled();
        });
    });

    describe('Connection State Management', () => {
        test('should prevent multiple simultaneous connections', async () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Mock initialize to be async
            const initializeSpy = jest.spyOn(client, 'initialize');
            
            // Start multiple initialization attempts
            const promise1 = client.initialize();
            const promise2 = client.initialize();
            const promise3 = client.initialize();
            
            await Promise.all([promise1, promise2, promise3]);
            
            // Should only initialize once due to connection guard
            expect(initializeSpy).toHaveBeenCalledTimes(3);
        });

        test('should handle connection errors gracefully', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Mock connection with error handler
            const mockConn = {
                onError: jest.fn(),
                onClose: jest.fn(),
                onOpen: jest.fn(),
                onMessage: jest.fn(),
                open: jest.fn(),
                close: jest.fn(),
                invalidate: jest.fn(),
                readyState: 0,
            };
            
            (client as any).conn = mockConn;
            
            // Simulate error
            const errorHandler = mockConn.onError.mock.calls[0]?.[0];
            if (errorHandler) {
                errorHandler({url: (client as any).url, error: 'Test error'});
            }
            
            // Should handle error without crashing
            expect(client.getIsDestroyed()).toBe(false);
        });
    });

    describe('Memory Leak Prevention', () => {
        test('should clear all timers on cleanup', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Set up timers
            (client as any).connectionTimeout = setTimeout(() => {}, 1000);
            (client as any).healthCheckInterval = setInterval(() => {}, 1000);
            
            const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
            const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
            
            client.destroy();
            
            expect(clearTimeoutSpy).toHaveBeenCalled();
            expect(clearIntervalSpy).toHaveBeenCalled();
        });

        test('should clear all callback references', () => {
            const client = new WebSocketClient(serverUrl, token);
            
            // Set callbacks
            const eventCallback = jest.fn();
            const connectCallback = jest.fn();
            const errorCallback = jest.fn();
            
            client.setEventCallback(eventCallback);
            client.setFirstConnectCallback(connectCallback);
            client.setErrorCallback(errorCallback);
            
            client.destroy();
            
            // Callbacks should be cleared
            expect((client as any).eventCallback).toBeUndefined();
            expect((client as any).firstConnectCallback).toBeUndefined();
            expect((client as any).errorCallback).toBeUndefined();
        });

        test('should complete all observables on manager destroy', () => {
            const serverUrl1 = 'https://test1.mattermost.com';
            const serverUrl2 = 'https://test2.mattermost.com';
            
            // Create observables
            const observable1 = WebsocketManager.observeWebsocketState(serverUrl1);
            const observable2 = WebsocketManager.observeWebsocketState(serverUrl2);
            
            const completeSpy1 = jest.fn();
            const completeSpy2 = jest.fn();
            
            observable1.subscribe({complete: completeSpy1});
            observable2.subscribe({complete: completeSpy2});
            
            // Destroy manager
            WebsocketManager.destroy();
            
            // Observables should complete
            expect(completeSpy1).toHaveBeenCalled();
            expect(completeSpy2).toHaveBeenCalled();
        });
    });
});
