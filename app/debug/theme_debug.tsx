// Debug component to check theme values
import React from 'react';
import { View, Text, TextInput, StyleSheet, Platform } from 'react-native';
import { useTheme } from '@context/theme';

const ThemeDebug = () => {
    const theme = useTheme();

    return (
        <View style={styles.container}>
            <Text style={styles.text}>Theme Debug Info:</Text>
            <Text style={styles.text}>Platform: {Platform.OS}</Text>
            <Text style={styles.text}>Type: {theme.type}</Text>
            <Text style={styles.text}>textSelectionColor: {theme.textSelectionColor || 'UNDEFINED'}</Text>
            <Text style={styles.text}>buttonBg: {theme.buttonBg}</Text>

            <Text style={styles.text}>Test TextInput (select text to see color):</Text>
            <TextInput
                style={styles.testInput}
                value="Select this text to test selection color"
                selectionColor={theme.textSelectionColor}
                selectionHandleColor={theme.textSelectionColor}
                cursorColor={theme.textSelectionColor}
                multiline={false}
            />

            <Text style={styles.text}>Raw color value: {theme.textSelectionColor}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 10,
        backgroundColor: '#f0f0f0',
        margin: 10,
    },
    text: {
        fontSize: 12,
        marginBottom: 5,
        color: '#000',
    },
    testInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 8,
        marginVertical: 5,
        backgroundColor: '#fff',
        color: '#000',
    },
});

export default ThemeDebug;
