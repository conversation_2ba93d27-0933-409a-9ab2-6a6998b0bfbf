// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {useEffect, useRef, useCallback, useState} from 'react';
import {AppState, type AppStateStatus} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';

import WebsocketManager from '@managers/websocket_manager';
import {useServerUrl} from '@context/server';
import {logDebug, logWarning} from '@utils/log';

interface UseWebSocketOptions {
    /**
     * Whether to automatically manage connection based on component focus
     */
    autoManage?: boolean;
    
    /**
     * Whether to pause connection when app goes to background
     */
    pauseOnBackground?: boolean;
    
    /**
     * Whether to pause connection when component loses focus
     */
    pauseOnBlur?: boolean;
    
    /**
     * Custom cleanup function to run on unmount
     */
    onCleanup?: () => void;
}

/**
 * Hook for managing WebSocket connections in React components
 * Provides automatic cleanup and lifecycle management
 */
export const useWebSocket = (options: UseWebSocketOptions = {}) => {
    const {
        autoManage = true,
        pauseOnBackground = true,
        pauseOnBlur = false,
        onCleanup,
    } = options;
    
    const serverUrl = useServerUrl();
    const navigation = useNavigation();
    const appStateRef = useRef<AppStateStatus>(AppState.currentState);
    const isFocusedRef = useRef(true);
    const cleanupRef = useRef<(() => void) | null>(null);
    
    const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
        const previousAppState = appStateRef.current;
        appStateRef.current = nextAppState;
        
        if (!autoManage || !pauseOnBackground) {
            return;
        }
        
        // Handle app going to background
        if (previousAppState === 'active' && nextAppState.match(/inactive|background/)) {
            logDebug('App went to background, WebSocket will be managed by WebsocketManager');
        }
        
        // Handle app coming to foreground
        if (previousAppState.match(/inactive|background/) && nextAppState === 'active') {
            logDebug('App came to foreground, WebSocket will be managed by WebsocketManager');
        }
    }, [autoManage, pauseOnBackground]);
    
    const handleNavigationStateChange = useCallback(() => {
        if (!autoManage || !pauseOnBlur) {
            return;
        }
        
        // Navigation state changes are handled by the focus effect
        logDebug('Navigation state changed');
    }, [autoManage, pauseOnBlur]);
    
    // Handle component focus/blur
    useFocusEffect(
        useCallback(() => {
            isFocusedRef.current = true;
            logDebug('Component focused');
            
            return () => {
                isFocusedRef.current = false;
                logDebug('Component blurred');
                
                if (pauseOnBlur && autoManage) {
                    // Component-specific cleanup can be added here if needed
                    // The WebsocketManager handles the actual connection management
                }
            };
        }, [pauseOnBlur, autoManage])
    );
    
    // Setup app state listener
    useEffect(() => {
        const subscription = AppState.addEventListener('change', handleAppStateChange);
        
        return () => {
            subscription?.remove();
        };
    }, [handleAppStateChange]);
    
    // Setup navigation listener
    useEffect(() => {
        const unsubscribe = navigation.addListener('state', handleNavigationStateChange);
        
        return unsubscribe;
    }, [navigation, handleNavigationStateChange]);
    
    // Main cleanup effect
    useEffect(() => {
        return () => {
            logDebug('WebSocket hook cleanup');
            
            // Run custom cleanup
            if (onCleanup) {
                try {
                    onCleanup();
                } catch (error) {
                    logWarning('Error in WebSocket cleanup callback', error);
                }
            }
            
            // Run stored cleanup function
            if (cleanupRef.current) {
                try {
                    cleanupRef.current();
                } catch (error) {
                    logWarning('Error in WebSocket stored cleanup', error);
                }
                cleanupRef.current = null;
            }
        };
    }, [onCleanup]);
    
    const getConnectionState = useCallback(() => {
        return WebsocketManager.isConnected(serverUrl);
    }, [serverUrl]);
    
    const observeConnectionState = useCallback(() => {
        return WebsocketManager.observeWebsocketState(serverUrl);
    }, [serverUrl]);
    
    const setCleanupFunction = useCallback((cleanup: () => void) => {
        cleanupRef.current = cleanup;
    }, []);
    
    return {
        isConnected: getConnectionState(),
        observeConnectionState,
        setCleanupFunction,
        isFocused: isFocusedRef.current,
        appState: appStateRef.current,
    };
};

/**
 * Hook for components that need to perform cleanup when WebSocket disconnects
 */
export const useWebSocketCleanup = (cleanup: () => void, deps: React.DependencyList = []) => {
    const serverUrl = useServerUrl();
    
    useEffect(() => {
        const subscription = WebsocketManager.observeWebsocketState(serverUrl).subscribe((state) => {
            if (state === 'not_connected') {
                try {
                    cleanup();
                } catch (error) {
                    logWarning('Error in WebSocket disconnect cleanup', error);
                }
            }
        });
        
        return () => {
            subscription.unsubscribe();
        };
    }, [serverUrl, ...deps]);
};

/**
 * Hook for components that need to react to WebSocket connection state changes
 */
export const useWebSocketState = () => {
    const serverUrl = useServerUrl();
    const [connectionState, setConnectionState] = useState<WebsocketConnectedState>('not_connected');
    
    useEffect(() => {
        const subscription = WebsocketManager.observeWebsocketState(serverUrl).subscribe((state) => {
            setConnectionState(state);
        });
        
        return () => {
            subscription.unsubscribe();
        };
    }, [serverUrl]);
    
    return connectionState;
};
